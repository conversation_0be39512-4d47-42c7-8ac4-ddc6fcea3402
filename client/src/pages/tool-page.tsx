import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { withAuth } from "@/lib/with-auth";
import DashboardLayoutWrapper from "@/components/layout/dashboard-layout";
import ColorPaletteGenerator from "@/components/tools/color-palette-generator";
import SEOA<PERSON>yzer from "@/components/tools/seo-analyzer";
import HeadlineAnalyzer from "@/components/tools/headline-analyzer";
import FocusGroupSimulator from "@/components/tools/focus-group-simulator";
import BuyerPersonaGenerator from "@/components/tools/buyer-persona-generator";
import DesignComplexityAnalyzer from "@/components/tools/design-complexity-analyzer";
import MoodBoard from "@/components/tools/mood-board";
import ProfessionalPostGeneratorNew from "@/components/tools/ProfessionalPostGeneratorNew";
import SEOGPTOptimizerApp from "@/pages/seo-gpt-optimizer/SEOGPTOptimizerApp";

import { getToolById, TOOL_ROUTES } from "@/data/ai-tools-data";
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Breadcrumbs, useToolBreadcrumbs } from "@/components/ui/breadcrumbs";

interface ToolPageProps {
  toolId?: string;
}

function ToolPageContent({ toolId }: ToolPageProps) {
  const [, setLocation] = useLocation();
  const [toolName, setToolName] = useState("Herramienta");
  const breadcrumbs = useToolBreadcrumbs(toolName);

  useEffect(() => {
    if (toolId) {
      const tool = getToolById(toolId);
      if (tool) {
        setToolName(tool.name);
      }
    }
  }, [toolId]);

  const renderToolComponent = () => {
    if (!toolId) return null;

    switch (toolId) {
      case "design-complexity-analyzer":
        return <DesignComplexityAnalyzer />;
      case "color-palette-generator":
        return <ColorPaletteGenerator />;
      case "seo-analyzer":
        return <SEOAnalyzer />;
      case "headline-analyzer":
        return <HeadlineAnalyzer />;
      case "focus-group-simulator":
        return <FocusGroupSimulator />;
      case "buyer-persona-generator":
        return <BuyerPersonaGenerator />;
      case "mood-board":
        return <MoodBoard />;
      case "generador-posts-profesional":
        return <ProfessionalPostGeneratorNew />;
      case "seo-gpt-optimizer":
        return <SEOGPTOptimizerApp />;

      default:
        return (
          <div className="container mx-auto p-6 text-center">
            <div className="py-12">
              <h2 className="text-2xl font-bold mb-4">
                Herramienta en desarrollo
              </h2>
              <p className="text-gray-600 mb-6">
                Esta herramienta está actualmente en desarrollo y estará
                disponible próximamente.
              </p>
              <Button
                onClick={() => setLocation("/dashboard/herramientas-marketing")}
                variant="outline"
              >
                Volver a herramientas
              </Button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="container mx-auto px-6 pt-6">
        {/* Breadcrumbs */}
        <Breadcrumbs items={breadcrumbs} className="mb-4" />

        <Button
          variant="outline"
          className="mb-6 border-blue-200 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 text-blue-600 transition-all"
          onClick={() => setLocation(TOOL_ROUTES.MARKETING_BASE)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver a herramientas
        </Button>
      </div>

      {renderToolComponent()}
    </div>
  );
}

function ToolPage({ toolId }: ToolPageProps) {
  const [toolTitle, setToolTitle] = useState("Herramienta");

  useEffect(() => {
    if (toolId) {
      const tool = getToolById(toolId);
      if (tool) {
        setToolTitle(tool.name);
      }
    }
  }, [toolId]);

  return (
    <DashboardLayoutWrapper pageTitle={toolTitle}>
      <ToolPageContent toolId={toolId} />
    </DashboardLayoutWrapper>
  );
}

// Solo para fines de desarrollo y visualización, luego volver a habilitar la autenticación
// export default withAuth(ToolPage);
export default ToolPage;

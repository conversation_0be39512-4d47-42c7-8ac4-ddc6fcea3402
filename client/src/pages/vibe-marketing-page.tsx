import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'wouter';
import {
  Search,
  Sparkles,
  TrendingUp,
  Target,
  Brain,
  Zap,
  ArrowRight,
  BarChart3,
  FileText,
  Globe,
  Star,
  Rocket,
  Palette
} from 'lucide-react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const VibeMarketingPage: React.FC = () => {
  const [, setLocation] = useLocation();
  const [hoveredTool, setHoveredTool] = useState<string | null>(null);
  const [selectedTool, setSelectedTool] = useState<string | null>(null);

  const tools = [
    {
      id: 'professional-post-generator',
      title: '<PERSON><PERSON>r de Posts',
      description: '<PERSON><PERSON> posts optimizados para redes sociales con IA avanzada',
      icon: <FileText className="h-8 w-8" />,
      gradient: 'from-cyan-500 to-blue-600',
      features: [
        'Generación con IA avanzada',
        'Optimizado para todas las plataformas',
        'Incluye hashtags relevantes',
        'Múltiples tonos de voz'
      ],
      path: '/dashboard/herramientas/generador-posts-profesional',
      isNew: false,
      status: 'Disponible',
      span: 'col-span-1 row-span-1',
      priority: 0
    },
    {
      id: 'seo-llm-optimizer',
      title: 'SEO + LLM Optimizer',
      description: 'Crea contenido que rankee en Google y sea recomendado por ChatGPT/Gemini',
      icon: <Search className="h-8 w-8" />,
      gradient: 'from-blue-500 to-purple-600',
      features: [
        'Análisis SERP automático',
        'Optimización dual: Google + LLMs',
        'Feedback en tiempo real',
        'Schema.org automático'
      ],
      path: '/dashboard/herramientas/seo-gpt-optimizer',
      isNew: true,
      status: 'Disponible',
      span: 'col-span-1 row-span-2',
      priority: 1
    },
    {
      id: 'viral-content-creator',
      title: 'Viral Content Creator',
      description: 'Genera contenido viral usando patrones de éxito comprobados',
      icon: <TrendingUp className="h-8 w-8" />,
      gradient: 'from-pink-500 to-red-600',
      features: [
        'Análisis de tendencias',
        'Patrones virales',
        'Hooks emocionales',
        'Multi-plataforma'
      ],
      path: '/vibe-marketing/viral-content-creator',
      isNew: true,
      status: 'Próximamente',
      span: 'col-span-1 row-span-1',
      priority: 2
    },
    {
      id: 'campaign-orchestrator',
      title: 'Campaign Orchestrator',
      description: 'Orquesta campañas completas con agentes IA especializados',
      icon: <Target className="h-8 w-8" />,
      gradient: 'from-green-500 to-teal-600',
      features: [
        'Agentes especializados',
        'Campañas multicanal',
        'Automatización inteligente',
        'ROI tracking'
      ],
      path: '/vibe-marketing/campaign-orchestrator',
      isNew: true,
      status: 'En desarrollo',
      span: 'col-span-1 row-span-1',
      priority: 3
    }
  ];

  const stats = [
    {
      label: 'Contenido optimizado',
      value: '10x',
      description: 'Más probabilidad de rankear',
      icon: <BarChart3 className="h-5 w-5" />,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      label: 'Tiempo ahorrado',
      value: '80%',
      description: 'En investigación SEO',
      icon: <Zap className="h-5 w-5" />,
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      label: 'Precisión LLM',
      value: '95%',
      description: 'En recomendaciones',
      icon: <Brain className="h-5 w-5" />,
      gradient: 'from-purple-500 to-pink-500'
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 350,
        damping: 25
      }
    }
  };

  return (
    <DashboardLayout pageTitle="Vibe Marketing">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="container mx-auto px-6 py-8">
          {/* Hero Section - Mismo estilo que Herramientas de Marketing */}
          <div className="relative rounded-2xl overflow-hidden mb-8 backdrop-blur-xl">
            <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
            <div className="relative px-8 py-16 md:py-20 md:px-12">
              <div className="max-w-5xl">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, ease: "easeOut" }}
                  className="mb-8"
                >
                  <motion.span
                    className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30"
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <Sparkles className="inline-block w-5 h-5 mr-2" />
                    Únicas en el Mundo
                  </motion.span>
                  <h1 className="text-4xl md:text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
                    Vibe{" "}
                    <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                      Marketing
                    </span>
                    <br className="hidden md:block" />
                    <span className="text-white/90 font-light">
                      herramientas revolucionarias
                    </span>
                  </h1>
                  <p className="text-white/90 text-xl md:text-2xl max-w-3xl font-light leading-relaxed">
                    Herramientas de marketing únicas en el mundo que solo existen en Emma.
                    Revoluciona tu estrategia con tecnología exclusiva.
                  </p>
                </motion.div>
              </div>
            </div>

            {/* Floating elements with glassmorphism */}
            <motion.div
              className="absolute right-0 bottom-0 transform translate-y-1/4 -translate-x-10 hidden lg:block"
              initial={{ opacity: 0, x: 100, rotate: -10 }}
              animate={{ opacity: 1, x: 0, rotate: 0 }}
              transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            >
              <div className="relative w-72 h-72">
                <motion.div
                  className="absolute w-40 h-40 bg-white/20 backdrop-blur-md rounded-2xl -top-32 -left-20 transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
                  animate={{ rotate: [12, 18, 12] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                >
                  <span className="text-7xl">🚀</span>
                </motion.div>
                <motion.div
                  className="absolute w-44 h-44 bg-white/15 backdrop-blur-md rounded-2xl -top-10 left-20 transform -rotate-6 flex items-center justify-center shadow-2xl border border-white/20"
                  animate={{ rotate: [-6, -12, -6] }}
                  transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                >
                  <span className="text-7xl">⚡</span>
                </motion.div>
                <motion.div
                  className="absolute w-36 h-36 bg-white/25 backdrop-blur-md rounded-2xl top-24 -left-10 transform rotate-45 flex items-center justify-center shadow-2xl border border-white/40"
                  animate={{ rotate: [45, 50, 45] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 2 }}
                >
                  <span className="text-6xl">🎯</span>
                </motion.div>
              </div>
            </motion.div>
          </div>

          {/* Herramientas Section - Estilo marketing tools */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <div className="mb-8">
              <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                Herramientas de Vibe Marketing
              </h2>
              <p className="text-gray-600 text-lg">
                Optimiza tu contenido para la era de la IA con herramientas revolucionarias
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {tools.map((tool, index) => (
                <motion.div
                  key={tool.id}
                  className="group relative bg-white/90 backdrop-blur-md rounded-2xl overflow-hidden border border-white/20 hover:border-white/40 flex flex-col h-full hover:shadow-2xl transition-all duration-500"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                  whileHover={{
                    y: -12,
                    scale: 1.02,
                    transition: { type: "spring", stiffness: 400, damping: 25 }
                  }}
                >
                  {/* Status badge - Más prominente */}
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className={`font-bold px-4 py-2 rounded-full shadow-xl text-sm ${
                      tool.status === 'Disponible'
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-2 border-green-300'
                        : tool.status === 'Próximamente'
                        ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white border-2 border-amber-300'
                        : 'bg-gradient-to-r from-blue-500 to-purple-500 text-white border-2 border-blue-300'
                    }`}>
                      {tool.status === 'Disponible' ? '✅ DISPONIBLE' : tool.status.toUpperCase()}
                    </Badge>
                  </div>

                  {/* Header with gradient */}
                  <div
                    className={`h-48 relative overflow-hidden bg-gradient-to-br ${tool.gradient}`}
                  >
                    {/* Glassmorphism overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20"></div>

                    <div className="pl-6 pt-6 text-white z-10 relative">
                      {/* Categoría de la herramienta */}
                      <div className="mb-2">
                        <Badge
                          variant="outline"
                          className="bg-white/30 backdrop-blur-md text-white border-white/50 font-semibold px-3 py-1 text-xs"
                        >
                          {tool.id === 'social-post-generator' ? '📱 REDES SOCIALES' :
                           tool.id === 'polotno-studio' ? '🎨 DISEÑO VISUAL' :
                           tool.id === 'seo-llm-optimizer' ? '🔍 SEO + IA' :
                           tool.id === 'viral-content-creator' ? '🔥 CONTENIDO VIRAL' :
                           '🎯 CAMPAÑAS'}
                        </Badge>
                      </div>
                      <h3 className="text-2xl font-bold mb-3 leading-tight">{tool.title}</h3>
                      {tool.isNew && (
                        <Badge
                          variant="outline"
                          className="bg-white/20 backdrop-blur-md text-white border-white/40 font-semibold px-3 py-1"
                        >
                          <Star className="w-3 h-3 mr-1" />
                          Nuevo
                        </Badge>
                      )}
                    </div>

                    {/* Icon with modern styling */}
                    <motion.div
                      className="absolute right-6 bottom-6 z-10"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <div className="w-28 h-28 rounded-2xl bg-white/20 backdrop-blur-md flex items-center justify-center border border-white/30 shadow-xl">
                        {tool.icon}
                      </div>
                    </motion.div>

                    {/* Modern geometric patterns */}
                    <div className="absolute right-0 top-0 w-40 h-40 bg-white/5 rounded-bl-[3rem] backdrop-blur-sm"></div>
                    <div className="absolute left-0 bottom-0 w-20 h-20 bg-white/10 rounded-tr-[2rem]"></div>
                  </div>

                  <div className="p-8 flex-grow relative">
                    <div className="flex items-start gap-4 relative">
                      <div className="flex-grow">
                        <p className="text-gray-700 mb-6 text-base leading-relaxed">
                          {tool.description}
                        </p>

                        <div className="mb-6">
                          <span className="text-base font-semibold text-gray-800 mb-4 block">
                            Con esta herramienta podrás:
                          </span>
                          <ul className="space-y-3">
                            {tool.features.slice(0, 3).map((feature, idx) => (
                              <motion.li
                                key={idx}
                                className="text-sm flex items-start"
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.3, delay: idx * 0.1 }}
                              >
                                <div className="min-w-[20px] h-[20px] rounded-full flex items-center justify-center mt-0.5 mr-3 shadow-sm bg-gradient-to-r from-[#3018ef]/20 to-[#dd3a5a]/20 border border-[#3018ef]/30">
                                  <ArrowRight size={12} className="text-[#3018ef]" />
                                </div>
                                <span className="text-gray-600 leading-relaxed">{feature}</span>
                              </motion.li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Modern button */}
                  <div className="p-6 border-t border-gray-100/50 relative z-10">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        className={`w-full py-4 text-white font-semibold rounded-xl text-lg shadow-xl hover:shadow-2xl transition-all duration-300 border-none`}
                        style={{
                          background: tool.status !== 'Disponible'
                            ? 'linear-gradient(135deg, #9ca3af, #6b7280)'
                            : `linear-gradient(135deg, ${tool.gradient.split(' ')[1]}, ${tool.gradient.split(' ')[3]})`,
                        }}
                        disabled={tool.status !== 'Disponible'}
                        onClick={() => {
                          if (tool.status !== 'Disponible') return;
                          setLocation(tool.path);
                        }}
                      >
                        {tool.status === 'Disponible' ? (
                          <>
                            {tool.id === 'social-post-generator' ? '📱 Crear Posts' :
                             tool.id === 'seo-llm-optimizer' ? '🔍 Optimizar SEO' :
                             'Usar Herramienta'}
                          </>
                        ) : (
                          `📋 Ver ${tool.status}`
                        )}{" "}
                        <ArrowRight size={18} className="ml-2" />
                      </Button>
                    </motion.div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* CTA Section - Estilo marketing tools */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <div className="relative rounded-2xl overflow-hidden bg-white/80 backdrop-blur-md border border-white/20 shadow-2xl">
              <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
              <div className="relative px-8 py-16 md:py-20 md:px-16">
                <div className="max-w-4xl mx-auto text-center">
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 1.0 }}
                    className="mb-8"
                  >
                    <motion.div
                      className="inline-flex items-center justify-center p-4 bg-white/20 backdrop-blur-md rounded-full mb-6 border border-white/30"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <Globe className="text-white" size={32} />
                    </motion.div>
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
                      El futuro del marketing{" "}
                      <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                        está aquí
                      </span>
                    </h2>
                    <p className="text-white/90 text-xl md:text-2xl mb-10 max-w-3xl mx-auto font-light leading-relaxed">
                      Optimiza tu contenido para la era de la IA. Rankea en Google y sé recomendado por ChatGPT, Gemini y otros LLMs.
                    </p>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        className="py-4 px-8 text-xl bg-white/90 backdrop-blur-md hover:bg-white text-[#3018ef] font-bold rounded-xl border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-300"
                        onClick={() => setLocation('/dashboard/herramientas/seo-gpt-optimizer')}
                      >
                        <Rocket className="mr-3 h-6 w-6" />
                        Usar SEO + LLM Optimizer
                      </Button>
                    </motion.div>
                    <motion.div
                      className="flex items-center justify-center gap-2 text-white/80 mt-4"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.8, delay: 1.2 }}
                    >
                      <Sparkles className="w-5 h-5 text-yellow-300" />
                      <span className="text-base font-medium">
                        ¡Ya disponible! La primera herramienta del mundo que optimiza para Google + ChatGPT
                      </span>
                      <Sparkles className="w-5 h-5 text-yellow-300" />
                    </motion.div>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default VibeMarketingPage;

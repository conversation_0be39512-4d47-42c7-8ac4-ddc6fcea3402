import { useState } from "react";

export interface BrandAnalysis {
  business_name: string;
  industry: string;
  value_proposition: string;
  voice_tone: string;
  target_audience: string;
  brand_colors: string[];
  services_products: string[];
  key_messages: string[];
  confidence_score: number;
}

export interface BrandData {
  brandUrl: string;
  brandDescription: string;
  businessName: string;
  brandColor: string;
  voice: string;
  topics: string[];
  ctas: string[];
  selectedTheme: string;
  selectedContentType: string;
  isAnalyzing: boolean;
  analysisComplete: boolean;
  brandAnalysis: BrandAnalysis | null;
  analysisError: string;
}

export const useBrandData = () => {
  const [brandUrl, setBrandUrl] = useState("");
  const [brandDescription, setBrandDescription] = useState("");
  const [businessName, setBusinessName] = useState("");
  const [brandColor, setBrandColor] = useState("#3018ef");
  const [voice, setVoice] = useState("");
  const [topics, setTopics] = useState([
    "La Revolución Del Marketing Autónomo",
    "La Clave Del Éxito",
    "Conectando Tu Marca Con Emma AI"
  ]);
  const [ctas, setCtas] = useState([
    "contact us",
    "book a demo at mywebsite.com/demo",
    "visit our website: mywebsite.com",
    "email <NAME_EMAIL>"
  ]);
  const [selectedTheme, setSelectedTheme] = useState("Balance");
  const [selectedContentType, setSelectedContentType] = useState("instagram_posts");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [brandAnalysis, setBrandAnalysis] = useState<BrandAnalysis | null>(null);
  const [analysisError, setAnalysisError] = useState("");

  const analyzeWebsite = async (url: string): Promise<boolean> => {
    setIsAnalyzing(true);
    setAnalysisComplete(false);
    setAnalysisError("");

    try {
      console.log("🔍 Starting website analysis for:", url);

      const response = await fetch('/api/v1/web/analyze-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      console.log("📡 API Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ API Error:", errorData);
        throw new Error(errorData.detail || 'Failed to analyze website');
      }

      const data = await response.json();
      console.log("📊 Analysis result:", data);
      
      if (data.status === 'success' && data.brand_info) {
        const brandInfo = data.brand_info;
        
        // Guardar análisis completo para uso interno
        setBrandAnalysis(brandInfo);
        
        // Llenar los datos automáticamente con lógica inteligente mejorada
        console.log("🤖 Auto-populating brand data...");

        setBusinessName(brandInfo.business_name || "");
        console.log("📝 Business name set to:", brandInfo.business_name);

        // Selección inteligente de color de marca
        const primaryColor = brandInfo.brand_colors?.[0] || "#3018ef";
        setBrandColor(primaryColor);
        console.log("🎨 Brand color set to:", primaryColor);

        // Generación inteligente de voz de marca
        const intelligentVoice = generateIntelligentVoice(brandInfo);
        setVoice(intelligentVoice);
        console.log("🗣️ Voice set to:", intelligentVoice.substring(0, 50) + "...");

        // Actualizar topics basados en servicios/productos con mayor inteligencia
        if (brandInfo.services_products && brandInfo.services_products.length > 0) {
          const smartTopics = generateSmartTopics(brandInfo);
          setTopics(smartTopics);
          console.log("📝 Topics set to:", smartTopics);
        }

        // Generar CTAs inteligentes basados en la industria y servicios
        const smartCTAs = generateSmartCTAs(brandInfo);
        setCtas(smartCTAs);
        console.log("📢 CTAs set to:", smartCTAs);

        // Seleccionar tema inteligentemente basado en la industria
        const industryThemeMap: Record<string, string> = {
          'tecnología': 'Interface',
          'inteligencia artificial': 'Interface',
          'marketing': 'Influencer',
          'diseño': 'Balance',
          'educación': 'Fonts',
          'salud': 'Simply Image',
          'finanzas': 'Tweet'
        };

        const suggestedTheme = industryThemeMap[brandInfo.industry?.toLowerCase()] || 'Balance';
        setSelectedTheme(suggestedTheme);
        console.log("🎭 Theme set to:", suggestedTheme);

        setAnalysisComplete(true);
        console.log("✅ Auto-population completed successfully!");
        return true;
      } else {
        throw new Error('Analysis failed');
      }
    } catch (error) {
      console.error('❌ Error analyzing website:', error);

      // Provide more specific error messages
      let errorMessage = "No pude acceder a tu sitio web. Por favor, describe tu marca en la caja de texto de abajo.";

      if (error instanceof Error) {
        if (error.message.includes('422')) {
          errorMessage = "Este sitio web no permite el análisis automático. Por favor, describe tu marca manualmente.";
        } else if (error.message.includes('timeout')) {
          errorMessage = "El análisis está tardando demasiado. Por favor, describe tu marca manualmente.";
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = "Error de conexión. Verifica tu conexión a internet e intenta de nuevo.";
        }
      }

      setAnalysisError(errorMessage);
      return false;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const processDescription = async (description: string) => {
    console.log("🔍 Processing manual brand description with AI analysis");
    setIsAnalyzing(true);
    setAnalysisError("");

    try {
      // Use AI to analyze the manual brand description
      const response = await fetch('/api/v1/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: `
Analiza la siguiente descripción de marca y extrae información estructurada. Si el texto parece ser un email o mensaje, extrae la información del negocio mencionado en el contenido.

DESCRIPCIÓN DE LA MARCA:
"${description}"

INSTRUCCIONES:
1. Si el texto menciona un negocio específico (como "Emma AI"), usa ese nombre
2. Si es un email o mensaje, extrae la información del negocio descrito en el contenido
3. Identifica la industria basándote en los servicios o productos mencionados
4. Extrae la propuesta de valor principal del negocio

Devuelve ÚNICAMENTE un objeto JSON válido con esta estructura exacta:
{
  "business_name": "nombre del negocio real extraído",
  "industry": "industria o sector identificado",
  "value_proposition": "propuesta de valor principal en una oración",
  "voice_tone": "tono y personalidad de la marca en 2-3 oraciones",
  "target_audience": "audiencia objetivo identificada",
  "brand_colors": ["#3018ef"],
  "services_products": ["servicio1", "servicio2", "servicio3"],
  "key_messages": ["mensaje1", "mensaje2", "mensaje3"],
  "confidence_score": 0.85
}

IMPORTANTE: Responde SOLO con el JSON válido, sin texto antes o después.`,
          type: 'brand_analysis',
          topic: 'brand analysis',
          tone: 'analytical',
          audience: 'business'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        let brandInfo;

        try {
          // Try to parse the AI response as JSON
          const aiResponse = data.result || data.content || data.text || "";
          console.log("🔍 Raw AI response:", aiResponse);

          // Clean the response - sometimes AI adds extra text
          let cleanedResponse = aiResponse.trim();

          // Extract JSON from the response if it's wrapped in text
          const jsonMatch = cleanedResponse.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            cleanedResponse = jsonMatch[0];
          }

          console.log("🧹 Cleaned response:", cleanedResponse);
          brandInfo = JSON.parse(cleanedResponse);

          if (brandInfo && brandInfo.business_name) {
            console.log("🤖 AI Analysis successful, auto-populating brand data...");

            // Save the analysis
            setBrandAnalysis(brandInfo);

            // Auto-populate ONLY with REAL data from analysis
            setBusinessName(brandInfo.business_name || "");
            setBrandColor(brandInfo.brand_colors?.[0] || "#3018ef");
            setVoice(brandInfo.voice_tone || "");

            // Generate smart topics ONLY from real analysis data
            const smartTopics = generateSmartTopics(brandInfo);
            if (smartTopics.length > 0) {
              setTopics(smartTopics);
            }

            // Generate smart CTAs ONLY from real analysis data
            const smartCTAs = generateSmartCTAs(brandInfo);
            if (smartCTAs.length > 0) {
              setCtas(smartCTAs);
            }

            // Set theme based on industry ONLY if industry is detected
            if (brandInfo.industry) {
              const industryThemeMap: Record<string, string> = {
                'tecnología': 'Interface',
                'inteligencia artificial': 'Interface',
                'marketing': 'Influencer',
                'diseño': 'Balance',
                'educación': 'Fonts',
                'salud': 'Simply Image',
                'finanzas': 'Tweet'
              };

              const suggestedTheme = industryThemeMap[brandInfo.industry?.toLowerCase()];
              if (suggestedTheme) {
                setSelectedTheme(suggestedTheme);
              }
            }

            setAnalysisComplete(true);
            console.log("✅ Manual description analysis completed successfully!");
            return true;
          } else {
            console.log("❌ AI analysis returned invalid data, leaving fields empty for manual completion");
            return false;
          }
        } catch (parseError) {
          console.warn("⚠️ Could not parse AI response as JSON, leaving fields empty for manual completion");
          return false;
        }
      }

      // If AI analysis fails completely, don't populate anything
      console.log("❌ AI analysis failed, leaving all fields empty for manual completion");
      return false;

    } catch (error) {
      console.error('❌ Error processing description:', error);

      // NO crear análisis fallback - dejar campos vacíos para completar manualmente
      console.log("❌ Analysis failed completely, user must fill fields manually");
      return false;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const addTopic = () => {
    setTopics([...topics, ""]);
  };

  const updateTopic = (index: number, value: string) => {
    const newTopics = [...topics];
    newTopics[index] = value;
    setTopics(newTopics);
  };

  const addCta = () => {
    setCtas([...ctas, ""]);
  };

  const removeCta = (index: number) => {
    setCtas(ctas.filter((_, i) => i !== index));
  };

  const updateCta = (index: number, value: string) => {
    const newCtas = [...ctas];
    newCtas[index] = value;
    setCtas(newCtas);
  };

  return {
    // State
    brandUrl,
    brandDescription,
    businessName,
    brandColor,
    voice,
    topics,
    ctas,
    selectedTheme,
    selectedContentType,
    isAnalyzing,
    analysisComplete,
    brandAnalysis,
    analysisError,

    // Setters
    setBrandUrl,
    setBrandDescription,
    setBusinessName,
    setBrandColor,
    setVoice,
    setTopics,
    setCtas,
    setSelectedTheme,
    setSelectedContentType,
    setAnalysisComplete,
    setAnalysisError,

    // Actions
    analyzeWebsite,
    processDescription,
    addTopic,
    updateTopic,
    addCta,
    removeCta,
    updateCta,
  };
};

// Funciones auxiliares para inteligencia mejorada
const generateIntelligentVoice = (brandInfo: BrandAnalysis): string => {
  const { voice_tone } = brandInfo;

  // SOLO usar el voice_tone del análisis real, nada más
  if (voice_tone && voice_tone.trim()) {
    console.log("🎯 Using analyzed voice tone:", voice_tone);
    return voice_tone;
  }

  // Si no hay voice_tone del análisis, devolver string vacío
  console.log("❌ No voice tone from analysis, returning empty");
  return '';
};

const generateSmartTopics = (brandInfo: BrandAnalysis): string[] => {
  const { services_products, key_messages, value_proposition } = brandInfo;

  const topics: string[] = [];

  // SOLO usar datos reales del análisis

  // 1. Propuesta de valor del análisis
  if (value_proposition && value_proposition.trim()) {
    topics.push(value_proposition);
  }

  // 2. Servicios/productos del análisis
  if (services_products && services_products.length > 0) {
    services_products.forEach(service => {
      if (service && service.trim()) {
        topics.push(service);
      }
    });
  }

  // 3. Mensajes clave del análisis
  if (key_messages && key_messages.length > 0) {
    key_messages.forEach(message => {
      if (message && message.trim()) {
        topics.push(message);
      }
    });
  }

  // Devolver solo los topics reales, sin fallbacks
  const uniqueTopics = [...new Set(topics)];
  console.log("📝 Generated topics from REAL analysis data only:", uniqueTopics);
  return uniqueTopics.slice(0, 3);
};

const generateSmartCTAs = (brandInfo: BrandAnalysis): string[] => {
  const { business_name, services_products } = brandInfo;

  const ctas: string[] = [];

  // SOLO usar datos reales del análisis

  // 1. CTAs basados en servicios REALES del análisis
  if (services_products && services_products.length > 0) {
    services_products.forEach(service => {
      if (service && service.trim()) {
        ctas.push(`Conoce ${service}`);
      }
    });
  }

  // 2. CTA con nombre real de la empresa del análisis
  if (business_name && business_name.trim()) {
    ctas.push(`Contacta ${business_name}`);
  }

  // Devolver solo CTAs reales, sin fallbacks genéricos
  const uniqueCTAs = [...new Set(ctas)];
  console.log("📢 Generated CTAs from REAL analysis data only:", uniqueCTAs);
  return uniqueCTAs;
};

// NO MORE SIMULATION FUNCTIONS - REMOVED

// NO FALLBACK FUNCTIONS - ONLY REAL ANALYSIS DATA ALLOWED

"""
API endpoints for post generation using multiple AI providers.
Refactored for maintainability and clean architecture.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException

from app.models.post_models import (
    PostGenerationRequest, 
    PostGenerationResponse, 
    GeneratedPost,
    MemeGenerationRequest
)
from app.services.post_generation_service import PostGenerationService
from app.services.meme_service import MemeService

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize services
post_generation_service = PostGenerationService()
meme_service = MemeService()


@router.post("/generate-batch", response_model=PostGenerationResponse)
async def generate_posts_batch(request: PostGenerationRequest):
    """
    Generate multiple posts using the intelligent content system.
    
    This endpoint uses AI-powered content intelligence to:
    - Process natural language descriptions
    - Analyze topic context
    - Generate unique visual hooks
    - Create contextually appropriate images
    - Optimize content for each platform
    
    Args:
        request: PostGenerationRequest with brand info, design config, and generation settings
        
    Returns:
        PostGenerationResponse with generated posts
    """
    try:
        logger.info("🚀 Starting intelligent batch post generation")
        
        # Validate request
        if not request.brandInfo:
            raise HTTPException(status_code=400, detail="Brand information is required")
        
        if not request.designConfig:
            raise HTTPException(status_code=400, detail="Design configuration is required")
        
        # Generate posts using the intelligent system
        response = await post_generation_service.generate_posts_batch(
            brand_info=request.brandInfo,
            design_config=request.designConfig,
            generation_config=request.generationConfig
        )
        
        if not response.success:
            logger.error(f"❌ Batch generation failed: {response.error}")
            raise HTTPException(status_code=500, detail=response.error)
        
        logger.info(f"✅ Batch generation completed: {response.total_generated} posts generated")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Critical error in batch generation endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/generate-similar", response_model=PostGenerationResponse)
async def generate_similar_posts(request: dict):
    """
    Generate posts similar to a reference post.

    This endpoint analyzes a reference post and generates new posts with similar:
    - Style and tone
    - Content structure
    - Visual elements
    - Template approach

    Args:
        request: Dict containing brandInfo, referencePost, and generationConfig

    Returns:
        PostGenerationResponse with generated similar posts
    """
    try:
        logger.info("🎯 Starting similar post generation")
        logger.info(f"📥 Request received: {request}")
        logger.info(f"📋 Request keys: {list(request.keys())}")

        # Extract data from request
        brand_info = request.get("brandInfo", {})
        reference_post = request.get("referencePost", {})
        generation_config = request.get("generationConfig", {})

        # Validate request
        if not brand_info:
            raise HTTPException(status_code=400, detail="Brand information is required")

        if not reference_post:
            raise HTTPException(status_code=400, detail="Reference post is required")

        # Create design config based on reference post
        design_config = {
            "template": reference_post.get("template", "Balance"),
            "platform": reference_post.get("platform", "Instagram"),
            "contentType": "similar_content",
            "referenceContent": reference_post.get("content", ""),
            "referenceStyle": reference_post.get("metadata", {})
        }

        logger.info(f"📋 Generating posts similar to: {reference_post.get('content', '')[:100]}...")
        logger.info(f"🖼️ Reference image URL: {reference_post.get('image_url', 'No image URL')}")
        logger.info(f"📊 Reference metadata: {reference_post.get('metadata', {})}")

        # Generate similar posts using the intelligent system
        response = await post_generation_service.generate_similar_posts(
            brand_info=brand_info,
            reference_post=reference_post,
            generation_config=generation_config
        )

        if not response.success:
            logger.error(f"❌ Similar post generation failed: {response.error}")
            raise HTTPException(status_code=500, detail=response.error)

        logger.info(f"✅ Similar post generation completed: {response.total_generated} posts generated")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Critical error in similar post generation endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/generate", response_model=PostGenerationResponse)
async def generate_single_post(request: PostGenerationRequest):
    """
    Generate a single post (legacy endpoint for backward compatibility).
    
    This endpoint maintains compatibility with existing frontend code
    while using the new intelligent content generation system.
    
    Args:
        request: PostGenerationRequest with brand info and design config
        
    Returns:
        PostGenerationResponse with single generated post
    """
    try:
        logger.info("🚀 Starting single post generation (legacy endpoint)")
        
        # Validate request
        if not request.brandInfo:
            raise HTTPException(status_code=400, detail="Brand information is required")
        
        if not request.designConfig:
            raise HTTPException(status_code=400, detail="Design configuration is required")
        
        # Generate single post using the intelligent system
        response = await post_generation_service.generate_single_post(
            brand_info=request.brandInfo,
            design_config=request.designConfig
        )
        
        if not response.success:
            logger.error(f"❌ Single post generation failed: {response.error}")
            raise HTTPException(status_code=500, detail=response.error)
        
        logger.info(f"✅ Single post generation completed")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Critical error in single post generation endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/generate-meme")
async def generate_meme_test(request: MemeGenerationRequest):
    """
    Test endpoint for meme generation.
    
    Args:
        request: MemeGenerationRequest with prompt and business info
        
    Returns:
        Generated meme result
    """
    try:
        logger.info(f"🎭 Generating test meme: {request.prompt}")
        
        result = await meme_service.generate_meme(
            prompt=request.prompt,
            business_name=request.business_name,
            industry=request.industry
        )
        
        if result.get("success"):
            logger.info("✅ Meme generated successfully")
        else:
            logger.warning(f"⚠️ Meme generation failed: {result.get('error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error in meme generation: {e}")
        raise HTTPException(status_code=500, detail=f"Meme generation failed: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check endpoint for the post generation service.
    
    Returns:
        Service health status
    """
    try:
        # Check if services are properly initialized
        services_status = {
            "post_generation_service": post_generation_service is not None,
            "meme_service": meme_service is not None,
            "ai_intelligence": hasattr(post_generation_service, 'ai_intelligence'),
            "content_service": hasattr(post_generation_service, 'content_service'),
            "image_service": hasattr(post_generation_service, 'image_service')
        }
        
        all_healthy = all(services_status.values())
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "services": services_status,
            "message": "All services operational" if all_healthy else "Some services may be unavailable"
        }
        
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Service health check failed"
        }


@router.get("/info")
async def service_info():
    """
    Get information about the post generation service capabilities.
    
    Returns:
        Service information and capabilities
    """
    return {
        "service": "Post Generation API",
        "version": "2.0.0",
        "features": {
            "intelligent_content_generation": True,
            "natural_language_processing": True,
            "context_aware_analysis": True,
            "dynamic_visual_hooks": True,
            "contextual_image_prompts": True,
            "platform_optimization": True,
            "batch_generation": True,
            "watermark_support": True
        },
        "supported_platforms": ["Instagram", "LinkedIn", "Facebook", "X"],
        "supported_templates": [
            "Balance", "Motivational", "Educational", "Creativo", 
            "Minimalista", "Profesional", "Viral"
        ],
        "ai_providers": {
            "content_intelligence": "Google Gemini",
            "image_generation": "Ideogram 3.0",
            "meme_generation": "Local Service"
        },
        "endpoints": {
            "/generate-batch": "Generate multiple posts with intelligent content system",
            "/generate": "Generate single post (legacy compatibility)",
            "/generate-meme": "Generate meme (test endpoint)",
            "/health": "Service health check",
            "/info": "Service information"
        }
    }
